# ===============================================
#  TCPing 批量端口连通性测试 v1.0
#  详细TCP连通性检测工具
#  AI增强版
#  日期: 2025-01-19
# ===============================================

param(
    [string]$DomainFile = "",
    [int]$Port = 443,
    [int]$Timeout = 3000,
    [int]$Count = 4,
    [int]$Threads = 10
)

# 颜色定义
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

# 显示帮助信息
function Show-Help {
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "           TCPing 批量端口连通性测试 v1.0" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""
    Write-Host "用法:" -ForegroundColor $Colors.Info
    Write-Host "  .\BatchTCPing_Fixed.ps1 -DomainFile domains.txt -Port 443 -Count 4" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor $Colors.Info
    Write-Host "  -DomainFile  域名列表文件路径" -ForegroundColor $Colors.Info
    Write-Host "  -Port        目标端口 (默认: 443)" -ForegroundColor $Colors.Info
    Write-Host "  -Timeout     连接超时时间(毫秒) (默认: 3000)" -ForegroundColor $Colors.Info
    Write-Host "  -Count       每个IP测试次数 (默认: 4)" -ForegroundColor $Colors.Info
    Write-Host "  -Threads     并发线程数 (默认: 10)" -ForegroundColor $Colors.Info
    Write-Host ""
}

# TCP连接测试
function Test-TCPConnection {
    param(
        [string]$IPAddress,
        [int]$Port,
        [int]$Timeout
    )
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $connectTask = $tcpClient.ConnectAsync($IPAddress, $Port)
        $completed = $connectTask.Wait($Timeout)
        $stopwatch.Stop()
        if ($completed -and $tcpClient.Connected) {
            $tcpClient.Close()
            return @{
                Success = $true
                Time = $stopwatch.ElapsedMilliseconds
                Error = $null
            }
        } else {
            $tcpClient.Close()
            return @{
                Success = $false
                Time = $Timeout
                Error = "连接超时"
            }
        }
    }
    catch {
        return @{
            Success = $false
            Time = $Timeout
            Error = $_.Exception.Message
        }
    }
}

# 域名DNS解析
function Resolve-DomainIPs {
    param([string]$Domain)
    try {
        $dnsResult = [System.Net.Dns]::GetHostAddresses($Domain)
        $ipv4Addresses = $dnsResult | Where-Object { $_.AddressFamily -eq 'InterNetwork' }
        return $ipv4Addresses | ForEach-Object { $_.IPAddressToString }
    }
    catch {
        Write-Host "DNS解析失败: $Domain - $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return @()
    }
}

# 批量测试主流程
function Start-BatchTCPing {
    param(
        [string[]]$Domains,
        [int]$Port,
        [int]$Timeout,
        [int]$Count,
        [int]$Threads
    )
    $results = @{}
    $totalDomains = $Domains.Count
    $currentDomain = 0
    
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "开始批量TCP端口连通性测试" -ForegroundColor $Colors.Header
    Write-Host "目标端口: $Port | 超时: ${Timeout}ms | 每IP测试: $Count 次" -ForegroundColor $Colors.Info
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""

    foreach ($domain in $Domains) {
        $currentDomain++
        Write-Host "[$currentDomain/$totalDomains] 正在处理: $domain" -ForegroundColor $Colors.Info

        # DNS解析
        Write-Host "  正在解析DNS..." -ForegroundColor $Colors.Info
        $ips = Resolve-DomainIPs -Domain $domain
        
        if ($ips.Count -eq 0) {
            Write-Host "  DNS解析失败" -ForegroundColor $Colors.Error
            $results[$domain] = @{}
            continue
        }

        Write-Host "  发现 $($ips.Count) 个IP: $($ips -join ', ')" -ForegroundColor $Colors.Success
        $results[$domain] = @{}

        # 针对每个IP测试
        foreach ($ip in $ips) {
            Write-Host "    IP: $ip" -ForegroundColor $Colors.Info
            
            $ipResults = @()
            $successCount = 0
            $totalTime = 0
            $minTime = [int]::MaxValue
            $maxTime = 0
            $times = @()
            
            # 多次测试
            for ($i = 1; $i -le $Count; $i++) {
                $result = Test-TCPConnection -IPAddress $ip -Port $Port -Timeout $Timeout
                $ipResults += $result

                if ($result.Success) {
                    $successCount++
                    $totalTime += $result.Time
                    $times += $result.Time
                    if ($result.Time -lt $minTime) { $minTime = $result.Time }
                    if ($result.Time -gt $maxTime) { $maxTime = $result.Time }
                    Write-Host "      第 $i 次: $($result.Time)ms" -ForegroundColor $Colors.Success
                } else {
                    Write-Host "      第 $i 次: 失败 ($($result.Error))" -ForegroundColor $Colors.Error
                }

                Start-Sleep -Milliseconds 100
            }
            
            # 统计
            $avgTime = if ($successCount -gt 0) { [math]::Round($totalTime / $successCount, 2) } else { 0 }
            $successRate = [math]::Round(($successCount / $Count) * 100, 2)
            $packetLoss = 100 - $successRate
            
            # 抖动（标准差）
            $jitter = 0
            if ($times.Count -gt 1) {
                $variance = ($times | ForEach-Object { [math]::Pow($_ - $avgTime, 2) } | Measure-Object -Sum).Sum / $times.Count
                $jitter = [math]::Round([math]::Sqrt($variance), 2)
            }
            
            $results[$domain][$ip] = @{
                Results = $ipResults
                SuccessCount = $successCount
                SuccessRate = $successRate
                PacketLoss = $packetLoss
                AverageTime = $avgTime
                MinTime = if ($successCount -gt 0) { $minTime } else { 0 }
                MaxTime = if ($successCount -gt 0) { $maxTime } else { 0 }
                Jitter = $jitter
                TotalTests = $Count
            }
            
            # IP汇总
            if ($successRate -eq 100) {
                Write-Host "      汇总: 成功率 ${successRate}% | 平均 ${avgTime}ms | 最快 ${minTime}ms | 最慢 ${maxTime}ms | 抖动 ${jitter}ms" -ForegroundColor $Colors.Success
            } elseif ($successRate -gt 0) {
                Write-Host "      汇总: 成功率 ${successRate}% | 平均 ${avgTime}ms | 丢包率 ${packetLoss}%" -ForegroundColor $Colors.Warning
            } else {
                Write-Host "      汇总: 成功率 ${successRate}% | 全部失败" -ForegroundColor $Colors.Error
            }
        }
    }
    
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "测试完成！" -ForegroundColor $Colors.Header
    
    return $results
}

# 生成报告
function New-Report {
    param($Results)
    
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "                详细测试报告" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""
    
    foreach ($domain in $Results.Keys) {
        Write-Host "域名: $domain" -ForegroundColor $Colors.Header
        Write-Host "----------------------------------------" -ForegroundColor $Colors.Header
        
        $domainResults = $Results[$domain]
        $totalIPs = $domainResults.Keys.Count
        $workingIPs = 0
        $bestIP = $null
        $bestTime = [int]::MaxValue
        
        foreach ($ip in $domainResults.Keys) {
            $ipData = $domainResults[$ip]
            
            if ($ipData.SuccessRate -gt 0) {
                $workingIPs++
                if ($ipData.AverageTime -lt $bestTime -and $ipData.SuccessRate -eq 100) {
                    $bestTime = $ipData.AverageTime
                    $bestIP = $ip
                }
            }

            # 显示IP详情
            $status = if ($ipData.SuccessRate -eq 100) { "优秀" } 
                     elseif ($ipData.SuccessRate -gt 80) { "良好" }
                     elseif ($ipData.SuccessRate -gt 50) { "一般" } 
                     else { "较差" }
            
            $color = if ($ipData.SuccessRate -eq 100) { $Colors.Success } 
                    elseif ($ipData.SuccessRate -gt 50) { $Colors.Warning } 
                    else { $Colors.Error }
            
            Write-Host "  IP: $ip" -ForegroundColor $color
            Write-Host "    成功率: $($ipData.SuccessRate)% ($($ipData.SuccessCount)/$($ipData.TotalTests))" -ForegroundColor $color
            if ($ipData.SuccessCount -gt 0) {
                Write-Host "    延迟统计: 平均 $($ipData.AverageTime)ms | 最低 $($ipData.MinTime)ms | 最高 $($ipData.MaxTime)ms" -ForegroundColor $color
                Write-Host "    网络抖动: $($ipData.Jitter)ms" -ForegroundColor $color
                if ($ipData.PacketLoss -gt 0) {
                    Write-Host "    丢包率: $($ipData.PacketLoss)%" -ForegroundColor $Colors.Warning
                }
            } else {
                Write-Host "    延迟统计: 无可用数据" -ForegroundColor $color
            }
            Write-Host "    状态评级: $status" -ForegroundColor $color
            Write-Host ""
        }
        
        # 域名汇总
        Write-Host "域名汇总:" -ForegroundColor $Colors.Info
        Write-Host "  总IP数: $totalIPs" -ForegroundColor $Colors.Info
        Write-Host "  可用IP数: $workingIPs" -ForegroundColor $Colors.Info
        if ($bestIP) {
            Write-Host ("  最佳IP: {0} ({1} ms)" -f $bestIP, $bestTime) -ForegroundColor $Colors.Success
        } else {
            Write-Host "  最佳IP: 无可用IP" -ForegroundColor $Colors.Error
        }
        Write-Host ""
        Write-Host "========================================" -ForegroundColor $Colors.Header
        Write-Host ""
    }
}

# 导出CSV报告
function Export-CSVReport {
    param($Results, $OutputFile)
    
    $csvData = @()
    
    foreach ($domain in $Results.Keys) {
        foreach ($ip in $Results[$domain].Keys) {
            $ipData = $Results[$domain][$ip]
            $csvData += [PSCustomObject]@{
                域名 = $domain
                IP = $ip
                成功率 = $ipData.SuccessRate
                丢包率 = $ipData.PacketLoss
                成功次数 = $ipData.SuccessCount
                总测试次数 = $ipData.TotalTests
                平均延迟 = $ipData.AverageTime
                最低延迟 = $ipData.MinTime
                最高延迟 = $ipData.MaxTime
                网络抖动 = $ipData.Jitter
                状态 = if ($ipData.SuccessRate -eq 100) { "优秀" } 
                      elseif ($ipData.SuccessRate -gt 80) { "良好" }
                      elseif ($ipData.SuccessRate -gt 50) { "一般" } 
                      else { "较差" }
            }
        }
    }
    
    $csvData | Export-Csv -Path $OutputFile -NoTypeInformation -Encoding UTF8
    Write-Host "CSV报告已保存到: $OutputFile" -ForegroundColor $Colors.Success
}

# 主函数
function Main {
    # 清屏并显示标题
    Clear-Host
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "           批量TCP连通性测试工具 v1.0" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""
    
    # 检查域名文件
    if (-not $DomainFile -or -not (Test-Path $DomainFile)) {
        if (-not $DomainFile) {
            Write-Host "错误: 请指定域名文件" -ForegroundColor $Colors.Error
        } else {
            Write-Host "错误: 域名文件不存在: $DomainFile" -ForegroundColor $Colors.Error
        }
        Write-Host ""
        Show-Help
        return
    }
    
    # 加载域名
    try {
        $domains = Get-Content $DomainFile | Where-Object { $_.Trim() -ne "" }
        Write-Host "已加载 $($domains.Count) 个域名" -ForegroundColor $Colors.Success
        Write-Host ""
    }
    catch {
        Write-Host "错误: 无法读取域名文件: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return
    }
    
    # 开始测试
    $results = Start-BatchTCPing -Domains $domains -Port $Port -Timeout $Timeout -Count $Count -Threads $Threads
    
    # 生成报告
    New-Report -Results $results
    
    # 导出CSV
    $csvFile = "TCPing_报告_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    Export-CSVReport -Results $results -OutputFile $csvFile
    
    Write-Host "测试完成！" -ForegroundColor $Colors.Success
}

# 入口点
Main