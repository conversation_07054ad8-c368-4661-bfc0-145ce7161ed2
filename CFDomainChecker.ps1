# ===============================================
# Cloudflare 域名检测工具 v1.0
# 功能：检测域名是否适合作为CF中转候选
# 作者：AI增强
# 日期：2025-01-19
# ===============================================

param(
    [string]$DomainFile = "",
    [int]$Timeout = 10000,
    [int]$Threads = 10,
    [switch]$Detailed = $false
)

# 颜色定义
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
    Good = "Green"
    Bad = "Red"
    Unknown = "Yellow"
}

# Cloudflare已知IP段（简化版）
$CFIPRanges = @(
    "104.16.", "104.17.", "104.18.", "104.19.", "104.20.", "104.21.", "104.22.", "104.23.", "104.24.", "104.25.", "104.26.", "104.27.", "104.28.", "104.29.", "104.30.", "104.31.",
    "172.64.", "172.65.", "172.66.", "172.67.", "172.68.", "172.69.", "172.70.", "172.71.",
    "198.41.", "162.159.", "188.114.", "190.93.", "197.234.", "108.162.",
    "141.101.", "103.21.", "103.22.", "103.31.", "131.0.", "173.245."
)

# 显示帮助信息
function Show-Help {
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "        CF域名检测工具 v1.0" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""
    Write-Host "用法:" -ForegroundColor $Colors.Info
    Write-Host "  .\CFDomainChecker.ps1 -DomainFile domains.txt" -ForegroundColor $Colors.Info
    Write-Host "  .\CFDomainChecker.ps1 -DomainFile domains.txt -Detailed" -ForegroundColor $Colors.Info
    Write-Host ""
    Write-Host "参数说明:" -ForegroundColor $Colors.Info
    Write-Host "  -DomainFile  域名列表文件路径" -ForegroundColor $Colors.Info
    Write-Host "  -Timeout     HTTP超时时间(毫秒) (默认: 10000)" -ForegroundColor $Colors.Info
    Write-Host "  -Threads     并发线程数 (默认: 10)" -ForegroundColor $Colors.Info
    Write-Host "  -Detailed    显示详细检测信息" -ForegroundColor $Colors.Info
    Write-Host ""
}

# 判断IP是否为Cloudflare
function Test-CloudflareIP {
    param([string]$IP)
    foreach ($range in $CFIPRanges) {
        if ($IP.StartsWith($range)) {
            return $true
        }
    }
    return $false
}

# DNS解析
function Resolve-DomainIPs {
    param([string]$Domain)
    try {
        $dnsResult = [System.Net.Dns]::GetHostAddresses($Domain)
        $ipv4Addresses = $dnsResult | Where-Object { $_.AddressFamily -eq 'InterNetwork' }
        return $ipv4Addresses | ForEach-Object { $_.IPAddressToString }
    }
    catch {
        return @()
    }
}

# 获取HTTP响应头
function Get-HTTPHeaders {
    param(
        [string]$Domain,
        [int]$Timeout
    )
    try {
        $url = "https://$Domain"
        $request = [System.Net.WebRequest]::Create($url)
        $request.Method = "HEAD"
        $request.Timeout = $Timeout
        $request.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        $response = $request.GetResponse()
        $headers = @{}
        foreach ($key in $response.Headers.AllKeys) {
            $headers[$key] = $response.Headers[$key]
        }
        $response.Close()
        return $headers
    }
    catch {
        return $null
    }
}

# 检测域名是否适合CF中转
function Test-CFOptimizationDomain {
    param([string]$Domain)
    $result = @{
        Domain = $Domain
        IsCFDomain = $false
        HasCFIPs = $false
        HasCFHeaders = $false
        IsAccessible = $false
        SecurityRisk = "低"
        DomainType = "未知"
        Recommendation = "不推荐"
        Score = 0
        Details = @()
        IPs = @()
        CFHeaders = @()
    }
    
    Write-Host "  正在检测: $Domain" -ForegroundColor $Colors.Info

    # 步骤1：DNS解析
    Write-Host "    [1/4] DNS解析..." -ForegroundColor $Colors.Info
    $ips = Resolve-DomainIPs -Domain $Domain
    $result.IPs = $ips
    
    if ($ips.Count -eq 0) {
        $result.Details += "DNS解析失败"
        Write-Host "      DNS解析失败" -ForegroundColor $Colors.Error
        return $result
    }

    Write-Host "      发现 $($ips.Count) 个IP: $($ips -join ', ')" -ForegroundColor $Colors.Success

    # 步骤2：判断Cloudflare IP
    Write-Host "    [2/4] Cloudflare IP检测..." -ForegroundColor $Colors.Info
    $cfIPCount = 0
    foreach ($ip in $ips) {
        if (Test-CloudflareIP -IP $ip) {
            $cfIPCount++
        }
    }
    
    if ($cfIPCount -gt 0) {
        $result.HasCFIPs = $true
        $result.Score += 40
        Write-Host "      检测到 $cfIPCount/$($ips.Count) 个Cloudflare IP" -ForegroundColor $Colors.Success
    } else {
        Write-Host "      未检测到Cloudflare IP" -ForegroundColor $Colors.Error
    }

    # 步骤3：HTTP响应头检测
    Write-Host "    [3/4] HTTP响应头检测..." -ForegroundColor $Colors.Info
    $headers = Get-HTTPHeaders -Domain $Domain -Timeout $Timeout
    
    if ($headers) {
        $result.IsAccessible = $true
        $result.Score += 20
        
        # 检查CF相关响应头
        $cfHeaders = @()
        foreach ($headerName in $headers.Keys) {
            $headerValue = $headers[$headerName]
            if ($headerName -match "^CF-" -or $headerValue -match "cloudflare") {
                $cfHeaders += "$headerName`: $headerValue"
            }
            if ($headerName -eq "Server" -and $headerValue -match "cloudflare") {
                $cfHeaders += "$headerName`: $headerValue"
            }
        }
        
        if ($cfHeaders.Count -gt 0) {
            $result.HasCFHeaders = $true
            $result.CFHeaders = $cfHeaders
            $result.Score += 30
            Write-Host "      检测到CF响应头: $($cfHeaders.Count) 个" -ForegroundColor $Colors.Success
        } else {
            Write-Host "      未检测到CF响应头" -ForegroundColor $Colors.Warning
        }

        Write-Host "      域名可通过HTTPS访问" -ForegroundColor $Colors.Success
    } else {
        Write-Host "      HTTPS访问失败" -ForegroundColor $Colors.Error
    }

    # 步骤4：安全性与类型分析
    Write-Host "    [4/4] 安全性分析..." -ForegroundColor $Colors.Info
    
    # 判断类型
    if ($Domain -match "\.(gov|mil)\.") {
        $result.DomainType = "政府"
        $result.SecurityRisk = "中"
    } elseif ($Domain -match "trump|biden|politics|election") {
        $result.DomainType = "政治"
        $result.SecurityRisk = "高"
    } elseif ($Domain -match "\.(com|net|org)$") {
        if ($Domain -match "^(www\.)?(google|microsoft|apple|amazon|facebook|twitter|github|stackoverflow|cloudflare)\.") {
            $result.DomainType = "知名品牌"
            $result.SecurityRisk = "低"
            $result.Score += 10
        } else {
            $result.DomainType = "商业"
            $result.SecurityRisk = "低"
        }
    } elseif ($Domain -match "\.(edu|ac\.)") {
        $result.DomainType = "教育"
        $result.SecurityRisk = "低"
    } elseif ($Domain -match "\.(tk|ml|ga|cf)$") {
        $result.DomainType = "免费域名"
        $result.SecurityRisk = "高"
        $result.Score -= 20
    } else {
        $result.DomainType = "普通"
    }
    
    # 域名判断
    $result.IsCFDomain = $result.HasCFIPs -and $result.IsAccessible
    
    # 根据分数和风险给出建议
    if ($result.Score -ge 70 -and $result.SecurityRisk -eq "低") {
        $result.Recommendation = "高度推荐"
    } elseif ($result.Score -ge 50 -and $result.SecurityRisk -ne "高") {
        $result.Recommendation = "推荐"
    } elseif ($result.Score -ge 30) {
        $result.Recommendation = "谨慎推荐"
    } else {
        $result.Recommendation = "不推荐"
    }

    # 对高安全风险的域名
    if ($result.SecurityRisk -eq "高") {
        $result.Recommendation = "不推荐 (安全风险)"
        $result.Score = [Math]::Max(0, $result.Score - 50)
    }

    Write-Host "      类型: $($result.DomainType) | 安全风险: $($result.SecurityRisk) | 推荐: $($result.Recommendation)" -ForegroundColor $Colors.Info
    return $result
}

# 生成汇总报告
function New-SummaryReport {
    param($Results)
    
    Write-Host ""
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "            CF候选域名检测报告" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""

    $recommended = $Results | Where-Object { $_.Recommendation -match "推荐" -and $_.Recommendation -notmatch "不推荐" }
    $notRecommended = $Results | Where-Object { $_.Recommendation -match "不推荐" }
    $conditional = $Results | Where-Object { $_.Recommendation -match "谨慎" }

    Write-Host "统计汇总:" -ForegroundColor $Colors.Header
    Write-Host "  总域名数: $($Results.Count)" -ForegroundColor $Colors.Info
    Write-Host "  推荐域名: $($recommended.Count)" -ForegroundColor $Colors.Good
    Write-Host "  谨慎推荐: $($conditional.Count)" -ForegroundColor $Colors.Warning
    Write-Host "  不推荐域名: $($notRecommended.Count)" -ForegroundColor $Colors.Bad
    Write-Host ""
    
    # 高度推荐的域名
    $highlyRecommended = $Results | Where-Object { $_.Recommendation -eq "高度推荐" } | Sort-Object Score -Descending
    if ($highlyRecommended.Count -gt 0) {
        Write-Host "★★ 高度推荐域名:" -ForegroundColor $Colors.Good
        foreach ($domain in $highlyRecommended) {
            Write-Host "  ✓ $($domain.Domain) (评分: $($domain.Score)/100)" -ForegroundColor $Colors.Good
            Write-Host "     类型: $($domain.DomainType) | IP数: $($domain.IPs.Count) | CF IP数: $(($domain.IPs | Where-Object { Test-CloudflareIP $_ }).Count)" -ForegroundColor $Colors.Info
        }
        Write-Host ""
    }

    # 推荐的域名
    $regularRecommended = $Results | Where-Object { $_.Recommendation -eq "推荐" } | Sort-Object Score -Descending
    if ($regularRecommended.Count -gt 0) {
        Write-Host "★ 推荐域名:" -ForegroundColor $Colors.Good
        foreach ($domain in $regularRecommended) {
            Write-Host "  ✓ $($domain.Domain) (评分: $($domain.Score)/100)" -ForegroundColor $Colors.Good
            Write-Host "     类型: $($domain.DomainType) | IP数: $($domain.IPs.Count) | CF IP数: $(($domain.IPs | Where-Object { Test-CloudflareIP $_ }).Count)" -ForegroundColor $Colors.Info
        }
        Write-Host ""
    }
    
    # 谨慎推荐的域名
    if ($conditional.Count -gt 0) {
        Write-Host "⚠️ 谨慎推荐域名:" -ForegroundColor $Colors.Warning
        foreach ($domain in $conditional) {
            Write-Host "  ⚠️ $($domain.Domain) (评分: $($domain.Score)/100)" -ForegroundColor $Colors.Warning
            Write-Host "     类型: $($domain.DomainType) | 风险: $($domain.SecurityRisk)" -ForegroundColor $Colors.Info
        }
        Write-Host ""
    }

    # 不推荐的域名
    if ($notRecommended.Count -gt 0) {
        Write-Host "✗ 不推荐域名:" -ForegroundColor $Colors.Bad
        foreach ($domain in $notRecommended) {
            $reason = if ($domain.SecurityRisk -eq "高") { "安全风险" }
                     elseif (-not $domain.HasCFIPs) { "无CF IP" }
                     elseif (-not $domain.IsAccessible) { "无法访问" }
                     else { "多种原因" }
            Write-Host "  ✗ $($domain.Domain) (评分: $($domain.Score)/100) - $reason" -ForegroundColor $Colors.Bad
        }
        Write-Host ""
    }
}

# 导出详细 CSV 报告
function Export-DetailedReport {
    param($Results, $OutputFile)
    
    $csvData = @()
    
    foreach ($result in $Results) {
        $csvData += [PSCustomObject]@{
            域名 = $result.Domain
            推荐级别 = $result.Recommendation
            评分 = $result.Score
            是CF域名 = $result.IsCFDomain
            有CF_IP = $result.HasCFIPs
            有CF响应头 = $result.HasCFHeaders
            可访问 = $result.IsAccessible
            域名类型 = $result.DomainType
            安全风险 = $result.SecurityRisk
            总IP数 = $result.IPs.Count
            CF_IP数量 = ($result.IPs | Where-Object { Test-CloudflareIP $_ }).Count
            CF响应头 = ($result.CFHeaders -join "; ")
            所有IP = ($result.IPs -join ", ")
        }
    }

    $csvData | Export-Csv -Path $OutputFile -NoTypeInformation -Encoding UTF8
    Write-Host "详细报告已保存到: $OutputFile" -ForegroundColor $Colors.Success
}

# 主函数
function Main {
    Clear-Host
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host "        CF候选域名检测工具 v1.0" -ForegroundColor $Colors.Header
    Write-Host "===============================================" -ForegroundColor $Colors.Header
    Write-Host ""

    # 检查参数
    if (-not $DomainFile -or -not (Test-Path $DomainFile)) {
        if (-not $DomainFile) {
            Write-Host "错误: 请指定域名文件" -ForegroundColor $Colors.Error
        } else {
            Write-Host "错误: 域名文件不存在: $DomainFile" -ForegroundColor $Colors.Error
        }
        Write-Host ""
        Show-Help
        return
    }

    # 读取域名列表
    try {
        $domains = Get-Content $DomainFile | Where-Object { $_.Trim() -ne "" -and -not $_.StartsWith("#") }
        Write-Host "已加载 $($domains.Count) 个域名进行检测" -ForegroundColor $Colors.Success
        Write-Host ""
    }
    catch {
        Write-Host "错误: 无法读取域名文件: $($_.Exception.Message)" -ForegroundColor $Colors.Error
        return
    }
    
    # 检测域名
    $results = @()
    $currentDomain = 0
    
    foreach ($domain in $domains) {
        $currentDomain++
        Write-Host "[$currentDomain/$($domains.Count)] 正在处理: $domain" -ForegroundColor $Colors.Header

        $result = Test-CFOptimizationDomain -Domain $domain.Trim()
        $results += $result

        # 显示临时结果
        $color = switch ($result.Recommendation) {
            "高度推荐" { $Colors.Good }
            "推荐" { $Colors.Good }
            "谨慎推荐" { $Colors.Warning }
            default { $Colors.Bad }
        }
        Write-Host "    结果: $($result.Recommendation) (评分: $($result.Score)/100)" -ForegroundColor $color
        Write-Host ""
    }

    # 生成报告
    New-SummaryReport -Results $results

    # 导出详细报告
    $csvFile = "CF域名检测报告_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    Export-DetailedReport -Results $results -OutputFile $csvFile

    Write-Host "检测完成！" -ForegroundColor $Colors.Success
    Write-Host ""

    # 显示最佳推荐
    $bestDomains = $results | Where-Object { $_.Recommendation -eq "高度推荐" } | Sort-Object Score -Descending | Select-Object -First 5
    if ($bestDomains.Count -gt 0) {
        Write-Host "★★ 最佳推荐CF候选域名:" -ForegroundColor $Colors.Good
        foreach ($domain in $bestDomains) {
            Write-Host "   $($domain.Domain)" -ForegroundColor $Colors.Good
        }
    }
}

# 程序入口点
Main
