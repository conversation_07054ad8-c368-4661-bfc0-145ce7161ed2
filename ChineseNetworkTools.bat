@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
echo.
echo ================================================================
echo                 中文网络工具箱 v1.0
echo ================================================================
echo.
powershell -Command "Write-Host '正在检测 PowerShell 环境...'" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到 PowerShell，请检查环境！
    pause
    exit /b 1
)
echo.
echo 欢迎使用中文网络工具箱
echo.
echo 1. 批量TCP端口连通性测试 - 调用 TCPing 工具
echo 2. Cloudflare 域名检测 - 调用 CF 检查工具
echo 3. 退出
echo.
set /p "CHOICE=请输入要选择的功能编号 (1-3): "
for /f "delims=0123456789" %%a in ("%CHOICE%") do set "CHOICE="
set "CHOICE=%CHOICE:.=%"
set "CHOICE=%CHOICE: =%"
if "%CHOICE%"=="1" goto :tcping_tool
if "%CHOICE%"=="2" goto :cf_checker_tool
if "%CHOICE%"=="3" goto :exit
echo.
echo 输入有误，请重新运行本工具。
pause
exit /b 1

:tcping_tool
echo.
echo ================================================================
echo                 批量TCP端口连通性测试
echo ================================================================
echo.
if not exist "BatchTCPing_Fixed.ps1" (
    echo [错误] 未找到 BatchTCPing_Fixed.ps1
    echo 请确保 BatchTCPing_Fixed.ps1 文件与本工具在同一目录下！
    pause
    exit /b 1
)
echo.
echo 请选择域名文件：
echo 1. domains.txt
echo 2. test_domains.txt
echo 3. 手动输入文件名
echo.
set "DOMAIN_FILE="
set /p "DFILE=请选择域名文件 (1-3，默认1): "
for /f "delims=0123456789" %%a in ("%DFILE%") do set "DFILE="
set "DFILE=%DFILE:.=%"
set "DFILE=%DFILE: =%"
if "%DFILE%"=="2" (
    set "DOMAIN_FILE=test_domains.txt"
) else if "%DFILE%"=="3" (
    set /p "DOMAIN_FILE=请输入域名文件名: "
) else (
    set "DOMAIN_FILE=domains.txt"
)
if not exist "!DOMAIN_FILE!" (
    echo.
    echo [错误] 未找到域名文件: !DOMAIN_FILE!
    pause
    exit /b 1
)
echo.
echo ================================================================
echo                    参数设置
echo ================================================================
echo.
set /p "PORT=请输入端口号 (默认 443): "
if "%PORT%"=="" set "PORT=443"
set /p "COUNT=请输入每个域名测试次数 (默认 4): "
if "%COUNT%"=="" set "COUNT=4"
set /p "TIMEOUT=请输入超时时间(毫秒) (默认 3000): "
if "%TIMEOUT%"=="" set "TIMEOUT=3000"
set /p "THREADS=请输入并发线程数 (默认 10): "
if "%THREADS%"=="" set "THREADS=10"
echo.
echo 测试参数：
echo.   域名文件: !DOMAIN_FILE!
echo.   端口号: %PORT%
echo.   测试次数: %COUNT%
echo.   超时时间: %TIMEOUT%ms
echo.   并发线程: %THREADS%
echo.
set /p "CONFIRM=确认开始测试？(Y/N): "
if /i not "%CONFIRM:~0,1%"=="Y" (
    echo.
    echo 已取消操作。
    pause
    exit /b 0
)
echo.
echo ================================================================
echo                    开始测试
echo ================================================================
powershell -ExecutionPolicy Bypass -File "BatchTCPing_Fixed.ps1" -DomainFile "!DOMAIN_FILE!" -Port %PORT% -Count %COUNT% -Timeout %TIMEOUT% -Threads %THREADS%
echo.
echo ================================================================
echo                    测试结束
echo ================================================================
echo.
echo 测试结果已输出到CSV文件，请查阅。
echo.
pause
exit /b 0

:cf_checker_tool
echo.
echo ================================================================
echo                Cloudflare 域名检测
echo ================================================================
echo.
if not exist "CFDomainChecker.ps1" (
    echo [错误] 未找到 CFDomainChecker.ps1
    echo 请确保 CFDomainChecker.ps1 文件与本工具在同一目录下！
    pause
    exit /b 1
)
echo.
echo 请选择域名文件：
echo 1. test_domains.txt
echo 2. domains.txt
echo 3. 手动输入文件名
echo.
set "DOMAIN_FILE="
set /p "DFILE=请选择域名文件 (1-3，默认1): "
for /f "delims=0123456789" %%a in ("%DFILE%") do set "DFILE="
set "DFILE=%DFILE:.=%"
set "DFILE=%DFILE: =%"
if "%DFILE%"=="2" (
    set "DOMAIN_FILE=domains.txt"
) else if "%DFILE%"=="3" (
    set /p "DOMAIN_FILE=请输入域名文件名: "
) else (
    set "DOMAIN_FILE=test_domains.txt"
)
if not exist "!DOMAIN_FILE!" (
    echo.
    echo [错误] 未找到域名文件: !DOMAIN_FILE!
    pause
    exit /b 1
)
echo.
echo ================================================================
echo                    参数设置
echo ================================================================
echo.
set /p "TIMEOUT=请输入HTTP超时时间(毫秒) (默认 10000): "
if "%TIMEOUT%"=="" set "TIMEOUT=10000"
set /p "THREADS=请输入并发线程数 (默认 10): "
if "%THREADS%"=="" set "THREADS=10"
set /p "DETAILED=是否显示详细检测信息？(Y/N, 默认N): "
if /i "%DETAILED:~0,1%"=="Y" (
    set "DETAILED_FLAG=-Detailed"
) else (
    set "DETAILED_FLAG="
)
echo.
echo 检测参数：
echo.   域名文件: !DOMAIN_FILE!
echo.   超时时间: %TIMEOUT%ms
echo.   并发线程: %THREADS%
echo.   详细模式: %DETAILED%
echo.
set /p "CONFIRM=确认开始检测？(Y/N): "
if /i not "%CONFIRM:~0,1%"=="Y" (
    echo.
    echo 已取消操作。
    pause
    exit /b 0
)
echo.
echo ================================================================
echo                    开始检测
echo ================================================================
powershell -ExecutionPolicy Bypass -File "CFDomainChecker.ps1" -DomainFile "!DOMAIN_FILE!" -Timeout %TIMEOUT% -Threads %THREADS% %DETAILED_FLAG%
echo.
echo ================================================================
echo                    检测结束
echo ================================================================
echo.
echo 检测结果已输出到CSV文件，请查阅。
echo.
pause
exit /b 0

:exit
echo.
echo 感谢使用本工具箱！
echo.
echo 主要功能：
echo - 100%% 本地运行，无需联网
echo - 支持 Cloudflare 域名检测
echo - 支持批量TCP端口连通性测试
echo - 结果可导出为CSV文件
echo.
echo 欢迎反馈建议与问题！
pause
exit /b 0    